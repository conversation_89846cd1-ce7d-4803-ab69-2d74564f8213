"""
日程管理助手Agent
专业的个人和团队日程规划助手，支持日程CRUD操作
"""
from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "schedule_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "日程管理助手",
    "brief": "专业的个人和团队日程规划助手",
    "category": "office",
    "avatar": "touxiang/9.png",
    "tags": ["日程", "时间管理", "会议", "提醒"]
}

def create_agent():
    """创建日程管理助手Agent实例"""
    # 创建Memory实例
    memory_db = SqliteMemoryDb(
        table_name="schedule_agent_memories",
        db_file="tmp/agents_memory.db"
    )
    memory = Memory(db=memory_db)
    
    return Agent(
        name="日程管理助手",
        model=OpenAILike(
            id="deepseek-ai/DeepSeek-V3",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="专业的个人和团队日程规划助手，支持日程CRUD操作",
        instructions="\n".join([
            "你是一位专业的日程管理助手",
            "你可以帮助用户创建、查询、修改和删除日程安排",
            "支持智能时间建议和冲突检测",
            "提供友好的中文交互体验",
            "当用户询问日程相关问题时，优先使用日程管理工具"
        ]),
        # Memory配置 - 启用用户记忆和会话摘要
        memory=memory,
        enable_user_memories=True,
        enable_session_summaries=True,
        # Storage配置
        storage=SqliteStorage(
            db_file="tmp/agents.db",
            table_name="schedule_agent_sessions"
        ),
        stream=True,
        markdown=True,
        show_tool_calls=True,
        add_history_to_messages=True,
        num_history_responses=3,
        add_datetime_to_instructions=True
    )