#!/usr/bin/env python3
"""
统一配置管理器
通过动态扫描文件夹，自动加载和管理Agents, Teams, 和 Workflows
"""
import os
import importlib
from typing import Dict, Any, List

def _dynamic_loader(directory: str, config_variable_name: str) -> Dict[str, Any]:
    """
    动态扫描指定目录，加载所有.py文件中的配置字典。

    :param directory: 要扫描的目录 (e.g., "agents", "teams", "flow").
    :param config_variable_name: 每个.py文件中包含配置的变量名 (e.g., "AGENT_METADATA", "TEAM_CONFIG").
    :return: 一个从文件名（不含.py）到其配置内容的字典.
    """
    registry = {}
    if not os.path.exists(directory):
        return registry

    for filename in os.listdir(directory):
        if filename.endswith(".py") and not filename.startswith("__"):
            module_name = filename[:-3]
            module_path = f"{directory}.{module_name}"
            try:
                module = importlib.import_module(module_path)
                if hasattr(module, config_variable_name):
                    registry[module_name] = getattr(module, config_variable_name)
            except Exception as e:
                print(f"❌ 在 {module_path} 中加载失败: {e}")
    return registry

class ConfigManager:
    """统一配置管理器"""

    def __init__(self):
        self.agents_cache = {}
        self.teams_cache = {}
        self.workflows_cache = {}
        self.reload_configs()

    def reload_configs(self):
        """重新加载所有配置"""
        print("🔄 正在从文件夹动态加载所有配置...")
        self.agents_cache.clear()
        self.teams_cache.clear()
        self.workflows_cache.clear()

        # 动态加载Agents配置和元数据
        from agents import AGENTS_REGISTRY
        self.agents_config = AGENTS_REGISTRY
        self.agents_metadata = _dynamic_loader("agents", "AGENT_METADATA")

        # 动态加载Teams和Workflows
        self.teams_config = _dynamic_loader("teams", "TEAM_CONFIG")
        self.workflows_config = _dynamic_loader("flow", "WORKFLOW_CONFIG")

        print("✅ 配置加载完成:")
        print(f"   - {len(self.agents_config)} 个 Agents (来自 agents/ 目录)")
        print(f"   - {len(self.teams_config)} 个 Teams (来自 teams/ 目录)")
        print(f"   - {len(self.workflows_config)} 个 Workflows (来自 flow/ 目录)")

    def list_all(self) -> Dict[str, List[str]]:
        """列出所有已加载的组件"""
        return {
            "agents": list(self.agents_config.keys()),
            "teams": list(self.teams_config.keys()),
            "workflows": list(self.workflows_config.keys()),
        }

    def get_agent_config(self, agent_id: str) -> Dict[str, Any]:
        """获取Agent的注册信息"""
        if agent_id not in self.agents_config:
            raise ValueError(f"找不到Agent: {agent_id}")
        return {"id": agent_id, "creator": self.agents_config[agent_id]}

    def get_team_config(self, team_id: str) -> Dict[str, Any]:
        """获取Team的配置"""
        if team_id not in self.teams_config:
            raise ValueError(f"找不到Team: {team_id}")
        return self.teams_config[team_id]

    def get_workflow_config(self, workflow_id: str) -> Dict[str, Any]:
        """获取Workflow的配置"""
        if workflow_id not in self.workflows_config:
            raise ValueError(f"找不到Workflow: {workflow_id}")
        return self.workflows_config[workflow_id]

    def get_agent_metadata(self, agent_id: str) -> Dict[str, Any]:
        """获取Agent的元数据信息"""
        if agent_id in self.agents_metadata:
            return self.agents_metadata[agent_id]
        
        # 如果没有找到元数据，返回默认值
        return {
            "display_name": agent_id,
            "brief": "智能助手",
            "category": "office",
            "avatar": "touxiang/agasf.png",
            "tags": ["助手"]
        }

    def create_agent(self, agent_id: str):
        """创建Agent实例"""
        if agent_id in self.agents_cache:
            return self.agents_cache[agent_id]
        
        from agents import create_agent_by_id
        agent = create_agent_by_id(agent_id)
        self.agents_cache[agent_id] = agent
        return agent

    def create_team(self, team_id: str):
        """创建Team实例"""
        if team_id in self.teams_cache:
            return self.teams_cache[team_id]

        from agno.team import Team
        
        config = self.get_team_config(team_id)
        members = [self.create_agent(member['agent_id']) for member in config.get('members', [])]
        
        team = Team(
            name=config.get("name", team_id),
            description=config.get("description", ""),
            members=members
        )
        team.collaboration_mode = config.get("collaboration_mode", "sequential")
        self.teams_cache[team_id] = team
        return team

    # create_workflow 可以在 main.py 中根据需要实现，因为它的执行逻辑更复杂
    # 这里只提供配置的获取

# 全局配置管理器实例
config_manager = ConfigManager()

if __name__ == "__main__":
    print("=== 配置管理器测试 ===")
    all_components = config_manager.list_all()
    print("\n可用的Agents:")
    for agent_id in all_components["agents"]:
        print(f"  - {agent_id}")
    
    print("\n可用的Teams:")
    for team_id in all_components["teams"]:
        print(f"  - {team_id}")

    print("\n可用的Workflows:")
    for workflow_id in all_components["workflows"]:
        print(f"  - {workflow_id}")

    print("\n测试创建 smart_routing_team:")
    try:
        team = config_manager.create_team("smart_routing_team")
        print(f"✅ 成功创建团队: {team.name}，拥有 {len(team.members)} 个成员。")
    except Exception as e:
        print(f"❌ 创建失败: {e}")
