import os
import json
import requests
from flask import Flask, render_template, send_from_directory, request, jsonify, Response
from flask_cors import CORS

app = Flask(__name__)
# 更详细的CORS配置
CORS(app, resources={r"/api/*": {"origins": "*", "supports_credentials": True}})

# 后端API地址
BACKEND_URL = "http://localhost:8001"

# 代理所有API请求到后端
@app.route('/api/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
def proxy_api(path):
    """代理所有API请求到main.py后端"""
    try:
        # 构建完整的后端URL
        backend_url = f"{BACKEND_URL}/api/{path}"
        
        # 获取原始请求数据
        data = request.get_data()
        headers = {key: value for key, value in request.headers if key.lower() != 'host'}
        
        # 处理CORS预检请求
        if request.method == 'OPTIONS':
            response = Response()
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
            return response
            
        # 转发请求到后端
        if request.method == 'GET':
            resp = requests.get(backend_url, params=request.args, headers=headers, stream=True)
        elif request.method == 'POST':
            resp = requests.post(backend_url, data=data, headers=headers, stream=True)
        elif request.method == 'PUT':
            resp = requests.put(backend_url, data=data, headers=headers)
        elif request.method == 'DELETE':
            resp = requests.delete(backend_url, headers=headers)
        
        # 处理流式响应
        if resp.headers.get('content-type', '').startswith('text/event-stream'):
            def generate():
                for chunk in resp.iter_content(chunk_size=1024):
                    if chunk:
                        yield chunk
            return Response(generate(), content_type='text/event-stream; charset=utf-8')
        
        # 处理普通响应
        response = Response(resp.content, status=resp.status_code)
        for key, value in resp.headers.items():
            if key.lower() not in ['content-encoding', 'content-length', 'transfer-encoding', 'connection']:
                response.headers[key] = value
        
        return response
        
    except requests.exceptions.ConnectionError:
        return jsonify({"error": "无法连接到后端服务，请确保main.py正在运行"}), 503
    except Exception as e:
        return jsonify({"error": f"代理请求失败: {str(e)}"}), 500

# 提供主页
@app.route('/')
def index():
    """提供主页"""
    return send_from_directory('.', 'index.html')

@app.route('/new_chat_interface.html')
def chat_interface():
    """提供聊天界面"""
    return send_from_directory('.', 'new_chat_interface.html')

@app.route('/chat_interface.js')
def chat_js():
    """提供聊天界面JS"""
    return send_from_directory('.', 'chat_interface.js')

@app.route('/<path:path>')
def serve_static(path):
    """提供静态文件"""
    return send_from_directory('.', path)

# 所有API请求都通过通用代理路由处理，删除显式路由以避免冲突

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    print(f"启动前端代理服务器在 http://localhost:{port}")
    print(f"后端API地址: {BACKEND_URL}")
    print("请确保main.py正在运行 (端口8001)")
    print("按Ctrl+C停止服务器")
    try:
        app.run(host='0.0.0.0', port=port, debug=False)
    except Exception as e:
        print(f"启动服务器时出错：{e}")
        print("请检查端口是否被占用，或者防火墙设置是否阻止了连接")