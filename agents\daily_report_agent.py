"""
日报专家Agent
专注于企业文档撰写，擅长简洁记录和展示工作进展。
"""
from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "daily_report_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "日报专家",
    "brief": "企业文档撰写专家，擅长工作进展记录",
    "category": "office",
    "avatar": "touxiang/13.png",
    "tags": ["日报", "工作总结", "文档", "记录"]
}

def create_agent():
    """创建日报专家Agent实例"""
    # 创建Memory实例
    memory_db = SqliteMemoryDb(
        table_name="daily_report_memories",
        db_file="tmp/agents_memory.db"
    )
    memory = Memory(db=memory_db)
    
    return Agent(
        name="日报",
        model=OpenAILike(
            id="deepseek-v3-250324",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是企业文档撰写专家，擅长简洁记录和展示工作进展，快速提炼关键信息。",
        instructions="\n".join([
            "技能: 高效整理信息，提炼关键任务与成果，简洁表达复杂内容。",
            "目标: 提供简洁的工作日报模板，突出核心任务与成果，避免冗余。",
            "要求: 日报简明扼要，突出重点，格式统一规范。",
            "输出格式: 包括日期、姓名、部门、工作内容、问题及解决方案、明日计划等，内容清晰简洁。",
            "以下是一个例子",
            """
            # 工作日报
            ## 基本信息
            **日期**：2025年7月15日  
            **员工姓名**：王大壮  
            **部门**：技术部  
            ## 一、今日工作内容
            ### 前端开发
            - **任务**：完成3个页面的前端代码编写。
            - **描述**：确保页面布局合理、交互逻辑清晰，为后续开发工作奠定基础。
            ### 项目会议
            - **任务**：参加项目进度会议，汇报当前的开发情况。
            - **描述**：与其他团队成员协调后续工作，确保项目按计划推进。
            ### 需求调研
            - **任务**：实地调研用户需求，完成需求确认。
            - **描述**：通过与用户的深入沟通，明确需求，避免后续开发中的误解。
            ## 二、今日遇到问题及解决方案
            ### 问题
            - **问题**：客户需求不明确。
            ### 解决方案
            - **解决措施**：通过实地调研和深入沟通，明确了用户的具体需求，完成了需求确认。
            ## 三、明日工作计划
            ### 任务
            - **页面设计**：根据确认的需求完成4个页面的设计工作。"""
        ]),
        # Memory配置 - 启用用户记忆和会话摘要
        memory=memory,
        enable_user_memories=True,
        enable_session_summaries=True,
        # Storage配置
        storage=SqliteStorage(
            db_file="tmp/agents.db",
            table_name="daily_newspaper_agent"
        ),
        # 历史消息配置
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )