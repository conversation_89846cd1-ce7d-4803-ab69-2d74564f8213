#!/usr/bin/env python3
"""
六顶思考帽工作流配置
"""

WORKFLOW_CONFIG = {
    "name": "六顶思考帽工作流",
    "display_name": "六顶思考帽思维工作流",
    "description": "系统化的六顶思考帽思维流程，依次引导用户从六个角度分析问题。",
    "brief": "系统化思维分析，从六个角度全面思考问题",
    "category": "analysis",
    "avatar": "touxiang/agag.png",
    "steps": [
        {"id": "white_hat", "name": "白帽", "type": "agent", "agent_id": "white_hat_agent", "description": "收集客观事实和信息。", "timeout": 300},
        {"id": "red_hat", "name": "红帽", "type": "agent", "agent_id": "red_hat_agent", "description": "表达情感和直觉。", "timeout": 300},
        {"id": "black_hat", "name": "黑帽", "type": "agent", "agent_id": "black_hat_agent", "description": "评估风险和问题。", "timeout": 300},
        {"id": "yellow_hat", "name": "黄帽", "type": "agent", "agent_id": "yellow_hat_agent", "description": "发现积极面和机会。", "timeout": 300},
        {"id": "green_hat", "name": "绿帽", "type": "agent", "agent_id": "green_hat_agent", "description": "探索创新和创意。", "timeout": 300},
        {"id": "blue_hat", "name": "蓝帽", "type": "agent", "agent_id": "blue_hat_agent", "description": "总结和过程控制。", "timeout": 300}
    ]
}
