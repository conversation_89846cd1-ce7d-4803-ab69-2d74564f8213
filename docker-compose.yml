version: '3.8'

services:
  agno-backend:
    build: .
    ports:
      - "8001:8001"
    volumes:
      - ./tmp:/app/tmp
      - ./touxiang:/app/touxiang
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  agno-frontend:
    build: .
    command: ["python", "serve_frontend.py"]
    ports:
      - "8080:8080"
    depends_on:
      - agno-backend
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  default:
    name: agno-network