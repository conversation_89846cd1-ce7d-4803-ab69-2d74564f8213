from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "new_media_copywriting_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "新媒体文案",
    "brief": "新媒体内容创作和文案撰写专家",
    "category": "creative",
    "avatar": "touxiang/17.png",
    "tags": ["新媒体", "文案", "营销", "创意"]
}

def create_agent():
    """创建新媒体文案Agent实例"""
    # 创建Memory实例
    memory_db = SqliteMemoryDb(
        table_name="new_media_copywriting_memories",
        db_file="tmp/agents_memory.db"
    )
    memory = Memory(db=memory_db)
    
    return Agent(
        name="新媒体文案",
        model=OpenAILike(
            id="deepseek-v3-250324",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是短视频内容策划专家，熟悉新媒体平台特点和用户行为，能创作吸引目标受众的脚本。",
        instructions="\n".join([
            "技能: 创意思维、市场分析、用户洞察和脚本撰写，能根据平台和受众定制脚本。",
            "目标: 提供符合需求的短视频脚本，确保吸引目标受众并促进行动。",
            "要求: 脚本需符合目的、受众和平台要求，语言风格适应新媒体，避免晦涩表达。",
            "输出格式: 使用Markdown格式，包含场景描述、对话、动作和镜头指示，附带注释说明。",
            "工作流程: 提取脚本目的、受众和平台，生成脚本大纲，撰写正文，添加注释解释设计。",
            "负面限制：不使用破折号、惊叹号、排比句，不做结尾总结。段落过渡自然，避免过渡词。表达简洁，避免复杂和生僻词汇。确保句子逻辑清晰，去除冗余和重复，避免冗长形容词或副词。不使用无意义修辞，避免复杂和模糊的短语。",
            "以下是一个短视频脚本的例子",
            """
            ## 开场 {吸引观众的注意力}
            **场景**：[描述开场场景]
            **人物**：[描述人物]
            **动作**：[描述动作]
            **对话**：[开场白]
            ## 主体内容 {详细描述产品、活动或品牌}
            - **场景**：[描述场景]
            - **人物**：[描述人物]
            - **动作**：[描述动作]
            - **对话**：[描述对话]
            - **镜头指示**：[描述镜头如何切换和拍摄]
            ## 结尾 {总结并强化品牌形象}
            - **场景**：[描述结尾场景]
            - **人物**：[描述人物]
            - **动作**：[描述动作]
            - **对话**：[结尾白]
            > **[精彩金句]** *（加粗标注）*
            ## 注释 {脚本构思和设计说明}
            在撰写这篇短视频脚本时，我考虑了以下几点：
            1. **目标受众分析**：深入了解目标受众的特点和需求，确保脚本内容能够引起他们的共鸣。
            2. **平台特性适应**：根据选择的发布平台，调整脚本的风格和格式，以适应平台特性和受众的观看习惯。
            3. **吸引力构建**：设计吸引眼球的开场和钩子，使受众愿意继续观看。
            4. **信息传达清晰**：确保脚本中的信息传达清晰、准确，避免误导受众。
            5. **行动引导明确**：在脚本中明确呼吁受众采取行动，如点击链接、参与活动等。
            6. **品牌一致性**：确保脚本中的信息与品牌形象和价值观保持一致，加强品牌认知。"""
        ]),
        # Memory配置 - 启用用户记忆和会话摘要
        memory=memory,
        enable_user_memories=True,
        enable_session_summaries=True,
        # Storage配置
        storage=SqliteStorage(
            db_file="tmp/agents.db",
            table_name="new_media_copywriting_agent"
        ),
        # 历史消息配置
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )