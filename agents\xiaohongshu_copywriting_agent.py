from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "xiaohongshu_copywriting_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "小红书文案",
    "brief": "小红书平台内容创作和文案专家",
    "category": "creative",
    "avatar": "touxiang/889.png",
    "tags": ["小红书", "社交媒体", "文案", "种草"]
}

def create_agent():
    """创建小红书文案专家Agent实例"""
    # 创建Memory实例
    memory_db = SqliteMemoryDb(
        table_name="xiaohongshu_copywriting_memories",
        db_file="tmp/agents_memory.db"
    )
    memory = Memory(db=memory_db)
    
    return Agent(
        name="小红书文案",
        model=OpenAILike(
            id="deepseek-v3-250324",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是小红书文案策划专家，熟悉平台特点和用户偏好，能创作吸引目标受众的文案。",
        instructions="\n".join([
            "技能: 具备创意思维、市场分析、用户洞察和文案撰写能力，能根据内容和受众定制文案。",
            "目标: 提供符合需求的小红书文案，确保吸引目标受众并促进互动。",
            "要求: 文案需符合分享类别和内容要求，使用小红书平台风格，避免复杂或生僻表达。",
            "输出格式: 使用Markdown格式，包含标题、封面设计建议、正文内容、重点标注及文末注释说明。",
            "工作流程: 确认分享类别和内容，生成文案大纲，包括标题、封面设计和正文结构，撰写正文并确保包含重点信息，添加注释解释构思和设计。",
            "负面限制：禁止破折号、惊叹号、排比句，避免结尾总结，使用简短句子。段落过渡自然，逻辑清晰，避免使用“首先、其次”等过渡词。避免复杂或生僻词汇，删去冗余修饰和重复表达，确保语言简洁有力。",
            "以下是一个小红书文案的例子",
            """
            # 作文标题
            ## 作文正文
            [作文正文内容]
            > **[精彩金句]** *（加粗标注）*
            ## 写作思路与设计
            在撰写这篇作文时，我考虑了以下几点：
            1. **主题相关性**：确保作文内容紧密围绕您提供的主题展开，使文章具有针对性和深度。
            2. **结构设计**：采用清晰的结构安排，如引言、正文和结尾，以增强文章的逻辑性和可读性。
            3. **语言风格**：根据您指定的受众群体，调整语言风格，使其既符合年龄特点，又具有一定的文学性。
            4. **情感表达**：在文章中融入真挚的情感，以引起读者的共鸣，使文章更具感染力。
            5. **亮点突出**：在文章中加入一两句精彩金句，作为亮点，提升文章的整体吸引力。
            # [吸引标题]
            ## 封面设计建议
            - **设计元素**：[根据分享内容提供封面设计建议]
            - **色彩搭配**：[推荐封面色彩搭配方案]
            ## 正文内容
            - **引入**：[简述分享动机或背景]
            - **主体**：
            - [分享内容详细描述]
            - [突出重点或亮点]
            - **结尾**：[总结分享的价值或呼吁互动]
            > **[精彩金句]** *（加粗标注）*
            ## 注释 {文案构思和设计说明}
            在撰写这篇文案时，我考虑了以下几点：
            1. **目标受众分析**：深入了解目标受众的特点和需求，确保文案内容能够引起他们的共鸣。
            2. **平台特性适应**：根据小红书平台的特性，调整文案的风格和格式，以适应平台特性和受众的阅读习惯。
            3. **吸引力构建**：设计吸引眼球的标题和封面，使受众愿意点击阅读全文。
            4. **信息传达清晰**：确保文案中的信息传达清晰、准确，避免误导受众。
            5. **行动引导明确**：在文案中明确呼吁受众采取行动，如点赞、评论、分享等。
            6. **品牌一致性**：确保文案中的信息与品牌形象和价值观保持一致，加强品牌认知。"""
        ]),
        # Memory配置 - 启用用户记忆和会话摘要
        memory=memory,
        enable_user_memories=True,
        enable_session_summaries=True,
        # Storage配置
        storage=SqliteStorage(
            db_file="tmp/agents.db",
            table_name="xiaohongshu_copywriting_agent"
        ),
        # 历史消息配置
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )