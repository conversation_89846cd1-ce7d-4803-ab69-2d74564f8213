from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "xiaohongshu_creation_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "小红书创作",
    "brief": "小红书内容创作和运营策略专家",
    "category": "creative",
    "avatar": "touxiang/886.png",
    "tags": ["小红书", "内容创作", "运营", "策略"]
}

def create_agent():
    """创建小红书文案写作专家Agent实例"""
    # 创建Memory实例
    memory_db = SqliteMemoryDb(
        table_name="xiaohongshu_creation_memories",
        db_file="tmp/agents_memory.db"
    )
    memory = Memory(db=memory_db)
    
    return Agent(
        name="小红书文案写作",
        model=OpenAILike(
            id="deepseek-v3-250324",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是小红书资深种草文案撰写专员，熟悉平台核心用户画像，深入理解平台算法偏好。",
        instructions="\n".join([
            "技能: 理解用户需求，生成创意内容，优化结构与文笔，调整风格与深度，增强文案吸引力。",
            "目标: 提供简洁、有力的文案，确保符合主题、字数和受众要求，激发用户共鸣和互动。",
            "要求: 文案要简洁、直接，突出核心内容，适合目标受众。语言要贴近小红书用户的生活方式，避免过于复杂或冗长的表达。",
            "工作流程: 确认主题、字数和受众，生成大纲，撰写正文并加粗精彩句子，确保过渡自然，语言简洁。",
            "负面限制: 避免破折号、惊叹号、排比句，结尾无总结，避免复杂词汇，删除冗余和重复表达。"
            "小红书文案大纲:通过吸引眼球的开头，简洁介绍产品亮点，突出核心优势。连接用户需求，引发情感共鸣，号召互动，鼓励用户参与。"
            "以下是一个小红书文案写作的例子",
            """
            正文: 想要拥有【关键词】的夏日光彩吗？🌸 这款【产品】不仅适合日常使用， 它的【特点/优势】让你感受到前所未有的舒适和效果。让你从内到外焕发光彩，成为焦点。 💬 你也尝试过类似的【体验/产品】吗？留言告诉我吧！
            精彩句子:
            “这款【产品】不仅适合日常使用，它的【特点/优势】让你感受到前所未有的舒适和效果。”
            “让你从内到外焕发光彩，成为焦点。”
            文末思路说明: 简洁、有力的语言驱动用户情感，突出产品优势，激发参与欲望。"""
        ]),
        # Memory配置 - 启用用户记忆和会话摘要
        memory=memory,
        enable_user_memories=True,
        enable_session_summaries=True,
        # Storage配置
        storage=SqliteStorage(
            db_file="tmp/agents.db",
            table_name="xiaohongshu_creation_agent"
        ),
        # 历史消息配置
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )