# 使用Python 3.11作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端代码
COPY agno/ agno/
COPY main.py .
COPY serve_frontend.py .

# 复制前端代码
COPY frontend/ frontend/

# 创建必要的目录
RUN mkdir -p tmp/uploads tmp/lancedb

# 暴露端口
EXPOSE 8001 8080

# 启动命令
CMD ["python", "main.py"]