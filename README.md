# Agno 统一智能平台 (v3.1.0)

欢迎使用 Agno 统一智能平台！这是一个高度模块化、可扩展的AI智能体（Agent）协作框架，旨在让开发者可以轻松地创建、管理和部署各种功能的AI助手、团队和工作流。

## ✨ 核心特性

- **纯动态加载**: 无需修改核心代码，只需在指定文件夹中添加或删除Python文件，即可动态地新增、移除或修改Agents, Teams, 和 Workflows。
- **模块化结构**: 清晰的目录结构，将Agents, Teams, Workflows, 和共享工具（Tools）完全分离，易于维护和扩展。
- **统一API接口**: 通过FastAPI提供了一套统一、现代的API接口，用于与单体Agent、团队或工作流进行交互。
- **可扩展的工具集**: 支持将任意函数封装为可被Agent调用的工具，并集中在 `tools/` 目录下进行管理。
- **现代化的Web技术**: 使用FastAPI的最新`lifespan`事件管理服务生命周期，代码规范，无历史包袱。

## 📂 项目结构

```
agno/
├── agents/              # 存放所有独立的Agent定义
│   ├── schedule_agent.py
│   └── ...
├── teams/               # 存放所有团队（Team）的配置文件
│   ├── smart_routing_team.py
│   └── ...
├── flow/                # 存放所有工作流（Workflow）的配置文件
│   ├── schedule_management_workflow.py
│   └── ...
├── tools/               # 存放所有可被Agent/Team共享的工具
│   ├── schedule_tools.py
│   └── ...
├── main.py              # FastAPI应用主入口
├── config_manager.py    # 核心的动态配置加载器
├── agents_config_universal.py # 存放API密钥、模型列表等不变的核心配置
└── README.md            # 项目文档
```

---

## 🚀 如何使用

### 启动项目

要运行整个平台，只需执行以下命令：

```bash
# 确保你已经激活了Python虚拟环境
python main.py
```

服务将在 `http://localhost:8001` 启动，你可以通过访问 `http://localhost:8001/docs` 查看并测试所有API。

### 🤖 如何管理Agent

- **新增Agent**: 在 `agents/` 目录下创建一个新的 `.py` 文件（例如 `my_new_agent.py`）。在这个文件中，定义Agent的创建函数和元数据。系统会自动发现并加载它。
- **修改Agent**: 直接编辑 `agents/` 目录下对应的Agent文件。
- **删除Agent**: 从 `agents/` 目录中删除对应的 `.py` 文件。

### 👥 如何管理Team

- **新增Team**: 在 `teams/` 目录下创建一个新的 `.py` 文件（例如 `my_new_team.py`）。在该文件中，定义一个名为 `TEAM_CONFIG` 的字典，描述团队的名称、成员、协作模式等。
- **修改Team**: 直接编辑 `teams/` 目录下对应的团队配置文件。
- **删除Team**: 从 `teams/` 目录中删除对应的 `.py` 文件。

### 🌊 如何管理Workflow

- **新增Workflow**: 在 `flow/` 目录下创建一个新的 `.py` 文件（例如 `my_new_workflow.py`）。在该文件中，定义一个名为 `WORKFLOW_CONFIG` 的字典，描述工作流的步骤和逻辑。
- **修改Workflow**: 直接编辑 `flow/` 目录下对应的文件。
- **删除Workflow**: 从 `flow/` 目录中删除对应的 `.py` 文件。

### 🛠️ 如何管理Tool

- **新增Tool**: 在 `tools/` 目录下创建一个新的 `.py` 文件，其中包含你希望Agent调用的函数。
- **注册Tool**: 打开根目录下的 `agents_config_universal.py` 文件，在 `TOOLS_CONFIG` 字典中添加你的新工具的配置，指定它的模块路径和可用函数。
- **在Agent中使用**: 在Agent的定义文件中，通过 `tools` 参数将注册好的工具注入给Agent。