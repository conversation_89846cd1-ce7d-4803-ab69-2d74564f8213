import sqlite3
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
import os

class ScheduleManager:
    """日程管理数据库操作类"""
    
    def __init__(self, db_path: str = "tmp/schedules.db"):
        """
        初始化日程管理器
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_path = db_path
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS schedules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    location TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            conn.commit()
    
    def _get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
        return conn
    
    def create_schedule(self, title: str, start_time: str, description: str = "", 
                       end_time: str = "", location: str = "") -> Dict[str, Any]:
        """
        创建新日程
        
        Args:
            title (str): 日程标题
            start_time (str): 开始时间
            description (str): 日程描述
            end_time (str): 结束时间
            location (str): 地点
            
        Returns:
            Dict[str, Any]: 创建的日程信息
        """
        now = datetime.now().isoformat()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO schedules (title, description, start_time, end_time, location, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (title, description, start_time, end_time, location, now, now))
            
            schedule_id = cursor.lastrowid
            conn.commit()
            
            # 返回创建的日程信息
            cursor.execute('SELECT * FROM schedules WHERE id = ?', (schedule_id,))
            row = cursor.fetchone()
            return dict(row) if row else {}
    
    def get_schedules(self, start_date: str = "", end_date: str = "", 
                     title_keyword: str = "") -> List[Dict[str, Any]]:
        """
        查询日程
        
        Args:
            start_date (str): 开始日期过滤（可选）
            end_date (str): 结束日期过滤（可选）
            title_keyword (str): 标题关键词过滤（可选）
            
        Returns:
            List[Dict[str, Any]]: 日程列表
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM schedules WHERE 1=1"
            params = []
            
            if start_date:
                query += " AND start_time >= ?"
                params.append(start_date)
            
            if end_date:
                # 如果end_date只是日期（没有时间），则添加23:59:59使其包含整天
                if len(end_date) == 10:  # YYYY-MM-DD 格式
                    end_datetime = end_date + " 23:59:59"
                else:
                    end_datetime = end_date
                query += " AND start_time <= ?"
                params.append(end_datetime)
            
            if title_keyword:
                query += " AND title LIKE ?"
                params.append(f"%{title_keyword}%")
            
            query += " ORDER BY start_time ASC"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    def update_schedule(self, schedule_id: int, title: str = "", description: str = "",
                       start_time: str = "", end_time: str = "", location: str = "") -> Dict[str, Any]:
        """
        更新日程
        
        Args:
            schedule_id (int): 日程ID
            title (str): 新标题（可选）
            description (str): 新描述（可选）
            start_time (str): 新开始时间（可选）
            end_time (str): 新结束时间（可选）
            location (str): 新地点（可选）
            
        Returns:
            Dict[str, Any]: 更新后的日程信息
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 首先检查日程是否存在
            cursor.execute('SELECT * FROM schedules WHERE id = ?', (schedule_id,))
            existing = cursor.fetchone()
            if not existing:
                return {"error": f"日程ID {schedule_id} 不存在"}
            
            # 构建更新语句
            update_fields = []
            params = []
            
            if title:
                update_fields.append("title = ?")
                params.append(title)
            if description:
                update_fields.append("description = ?")
                params.append(description)
            if start_time:
                update_fields.append("start_time = ?")
                params.append(start_time)
            if end_time:
                update_fields.append("end_time = ?")
                params.append(end_time)
            if location:
                update_fields.append("location = ?")
                params.append(location)
            
            if not update_fields:
                return {"error": "没有提供要更新的字段"}
            
            # 添加更新时间
            update_fields.append("updated_at = ?")
            params.append(datetime.now().isoformat())
            params.append(schedule_id)
            
            query = f"UPDATE schedules SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(query, params)
            conn.commit()
            
            # 返回更新后的日程信息
            cursor.execute('SELECT * FROM schedules WHERE id = ?', (schedule_id,))
            row = cursor.fetchone()
            return dict(row) if row else {}
    
    def delete_schedule(self, schedule_id: int) -> Dict[str, Any]:
        """
        删除日程
        
        Args:
            schedule_id (int): 日程ID
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 首先检查日程是否存在
            cursor.execute('SELECT * FROM schedules WHERE id = ?', (schedule_id,))
            existing = cursor.fetchone()
            if not existing:
                return {"error": f"日程ID {schedule_id} 不存在"}
            
            # 删除日程
            cursor.execute('DELETE FROM schedules WHERE id = ?', (schedule_id,))
            conn.commit()
            
            return {"success": f"日程ID {schedule_id} 已成功删除", "deleted_schedule": dict(existing)}


# 创建全局日程管理器实例
schedule_manager = ScheduleManager()


def create_schedule(title: str, start_time: str, description: str = "", 
                   end_time: str = "", location: str = "") -> str:
    """
    创建新日程
    
    Args:
        title (str): 日程标题，必填
        start_time (str): 开始时间，格式如 "2024-01-15 09:00" 或 "2024-01-15 09:00:00"
        description (str): 日程描述，可选
        end_time (str): 结束时间，格式同开始时间，可选
        location (str): 地点，可选
        
    Returns:
        str: JSON格式的创建结果
    """
    try:
        result = schedule_manager.create_schedule(title, start_time, description, end_time, location)
        return json.dumps({"success": True, "schedule": result}, ensure_ascii=False, indent=2)
    except Exception as e:
        return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False, indent=2)


def get_schedules(start_date: str = "", end_date: str = "", title_keyword: str = "") -> str:
    """
    查询日程列表
    
    Args:
        start_date (str): 开始时间过滤，格式如 "2024-01-15" 或 "2024-01-15 09:00:00"，可选
        end_date (str): 结束时间过滤，格式如 "2024-01-20" 或 "2024-01-20 18:00:00"，可选  
        title_keyword (str): 标题关键词搜索，可选
        
    Returns:
        str: JSON格式的日程列表
    """
    try:
        schedules = schedule_manager.get_schedules(start_date, end_date, title_keyword)
        return json.dumps({"success": True, "schedules": schedules, "count": len(schedules)}, 
                         ensure_ascii=False, indent=2)
    except Exception as e:
        return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False, indent=2)


def get_schedules_by_time_range(hours_from_now: int, title_keyword: str = "") -> str:
    """
    查询从现在开始指定小时数内的日程
    
    Args:
        hours_from_now (int): 从现在开始的小时数，如26表示未来26小时
        title_keyword (str): 标题关键词搜索，可选
        
    Returns:
        str: JSON格式的日程列表
    """
    try:
        from datetime import datetime, timedelta
        
        now = datetime.now()
        end_time = now + timedelta(hours=hours_from_now)
        
        start_datetime = now.strftime("%Y-%m-%d %H:%M:%S")
        end_datetime = end_time.strftime("%Y-%m-%d %H:%M:%S")
        
        schedules = schedule_manager.get_schedules(start_datetime, end_datetime, title_keyword)
        
        return json.dumps({
            "success": True, 
            "schedules": schedules, 
            "count": len(schedules),
            "time_range": f"从现在({start_datetime})到{hours_from_now}小时后({end_datetime})"
        }, ensure_ascii=False, indent=2)
    except Exception as e:
        return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False, indent=2)


def update_schedule(schedule_id: int, title: str = "", description: str = "",
                   start_time: str = "", end_time: str = "", location: str = "") -> str:
    """
    更新日程信息
    
    Args:
        schedule_id (int): 要更新的日程ID，必填
        title (str): 新标题，可选
        description (str): 新描述，可选
        start_time (str): 新开始时间，可选
        end_time (str): 新结束时间，可选
        location (str): 新地点，可选
        
    Returns:
        str: JSON格式的更新结果
    """
    try:
        result = schedule_manager.update_schedule(schedule_id, title, description, start_time, end_time, location)
        if "error" in result:
            return json.dumps({"success": False, "error": result["error"]}, ensure_ascii=False, indent=2)
        return json.dumps({"success": True, "schedule": result}, ensure_ascii=False, indent=2)
    except Exception as e:
        return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False, indent=2)


def delete_schedule(schedule_id: int) -> str:
    """
    删除日程
    
    Args:
        schedule_id (int): 要删除的日程ID
        
    Returns:
        str: JSON格式的删除结果
    """
    try:
        result = schedule_manager.delete_schedule(schedule_id)
        if "error" in result:
            return json.dumps({"success": False, "error": result["error"]}, ensure_ascii=False, indent=2)
        return json.dumps({"success": True, "message": result["success"], 
                          "deleted_schedule": result["deleted_schedule"]}, ensure_ascii=False, indent=2)
    except Exception as e:
        return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False, indent=2)
