// 头像映射配置
const AVATAR_MAP = {
    // Agent头像映射
    agents: {
        // 'legal_agent': 'touxiang/3.png',
        'case_analyst': 'touxiang/4.png',
        'legal_researcher': 'touxiang/5.png',
        'document_drafter': 'touxiang/7.png',
        'final_reviewer': 'touxiang/8.png',
        'schedule_agent': 'touxiang/9.png',
        'daily_report_agent': 'touxiang/11.png',
        'white_hat_agent': 'touxiang/12.png',
        'red_hat_agent': 'touxiang/13.png',
        'black_hat_agent': 'touxiang/15.png',
        'yellow_hat_agent': 'touxiang/16.png',
        'green_hat_agent': 'touxiang/19.png',
        'blue_hat_agent': 'touxiang/21.png'
    },
    
    // Team头像映射
    teams: {
        'legal_document_team': 'touxiang/213.png',
        'legal_consulting_team': 'touxiang/abgag.png',
        'smart_routing_team': 'touxiang/adca.png'
    },
    
    // Flow头像映射
    flows: {
        'legal_document_workflow': 'touxiang/afaf.png',
        'schedule_management_workflow': 'touxiang/aga.png',
        'six_hats_workflow': 'touxiang/agag.png'
    },
    
    // 默认头像
    defaults: {
        agent: 'touxiang/agasf.png',
        team: 'touxiang/agasga.png',
        flow: 'touxiang/agha.png',
        user: 'touxiang/agva.png'
    }
};

// 获取头像URL的函数
function getAvatarUrl(type, id) {
    // type: 'agent', 'team', 'flow'
    // id: agent_id, team_id, flow_id
    
    if (type === 'user') {
        return AVATAR_MAP.defaults.user;
    }
    
    const map = AVATAR_MAP[type + 's'];
    if (map && map[id]) {
        return map[id];
    }
    
    // 返回默认头像
    return AVATAR_MAP.defaults[type] || AVATAR_MAP.defaults.agent;
}

// 获取分类颜色
function getCategoryColor(category) {
    const colorMap = {
        'legal': '#007bff',
        'office': '#28a745', 
        'creative': '#fd7e14',
        'analysis': '#6f42c1',
        'workflow': '#20c997',
        'team': '#dc3545'
    };
    return colorMap[category] || '#6c757d';
}

// 获取分类图标
function getCategoryIcon(category) {
    const iconMap = {
        'legal': 'fas fa-balance-scale',
        'office': 'fas fa-briefcase',
        'creative': 'fas fa-pen-fancy', 
        'analysis': 'fas fa-chart-bar',
        'workflow': 'fas fa-stream',
        'team': 'fas fa-users'
    };
    return iconMap[category] || 'fas fa-robot';
}

// 导出给全局使用
if (typeof window !== 'undefined') {
    window.AVATAR_CONFIG = {
        AVATAR_MAP,
        getAvatarUrl
    };
}