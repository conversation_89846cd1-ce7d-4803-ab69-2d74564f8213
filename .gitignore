# ========================================
# Agno AI Agent 项目 - Git 排除规则
# ========================================

# ===================
# Python 相关
# ===================
# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# ===================
# AI/ML 相关
# ===================
# 模型文件和缓存
*.model
*.pkl
*.joblib
models/
checkpoints/
.transformers_cache/
.cache/
.huggingface/

# 向量数据库
*.db
*.sqlite
*.sqlite3
lancedb/
vector_db/
embeddings/

# ===================
# 临时文件和数据
# ===================
# 临时目录
tmp/
temp/
archived_files/
backup/
logs/
*.log

# 数据文件
data/
datasets/
*.csv
*.json
*.jsonl
*.parquet

# 输出文件
output/
results/
reports/
*.txt
*.md
!README.md
!requirements.txt

# ===================
# 敏感信息
# ===================
# API 密钥和配置
.env*
*.key
*.pem
secrets/
config/local*
*_secrets.py

# ===================
# 开发工具
# ===================
# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# ===================
# 系统文件
# ===================
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# ===================
# Web 相关
# ===================
# Node.js (如果有前端组件)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===================
# Agno 项目特定
# ===================
# Agent 运行时数据
agents.db
schedules.db
chat_history/
sessions/
memory/

# 知识库文件
knowledge_base/*.pdf
knowledge_base/*.docx
knowledge_base/*.txt

# 测试和调试文件
test_*.py
debug_*.py
*_test.py
*_debug.py

# 临时 HTML 文件
test_*.html
temp_*.html
debug_*.html

# 配置备份
*_backup.py
backup_*.py 