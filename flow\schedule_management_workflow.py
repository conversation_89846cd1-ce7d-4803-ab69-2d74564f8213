#!/usr/bin/env python3
"""
智能日程管理工作流配置
"""

WORKFLOW_CONFIG = {
    "name": "智能日程管理流程",
    "display_name": "智能日程管理工作流",
    "description": "智能化的日程规划和管理流程",
    "brief": "智能分析、规划和优化个人日程安排",
    "category": "office",
    "avatar": "touxiang/aga.png",
    "steps": [
        {
            "id": "schedule_analysis",
            "name": "日程需求分析", 
            "type": "agent",
            "agent_id": "schedule_agent",
            "description": "分析用户日程需求和约束",
            "timeout": 120
        },
        {
            "id": "schedule_planning",
            "name": "日程规划",
            "type": "agent",
            "agent_id": "schedule_agent", 
            "description": "制定具体的日程安排",
            "timeout": 180,
            "depends_on": ["schedule_analysis"]
        },
        {
            "id": "schedule_optimization",
            "name": "日程优化",
            "type": "agent",
            "agent_id": "schedule_agent",
            "description": "优化日程安排，避免冲突",
            "timeout": 120,
            "depends_on": ["schedule_planning"]
        }
    ],
    "conditions": {
        "schedule_analysis.success": "schedule_planning",
        "schedule_planning.success": "schedule_optimization", 
        "schedule_optimization.success": "complete"
    },
    "error_handling": "continue",
    "max_retries": 1
}
