# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.cursor/
.kiro/
.idea/

# Git
.git/
.gitignore

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Development files
debug_*.py
test_*.py
*_test.py

# Temporary files
tmp/uploads/*
tmp/lancedb/*
*.tmp
*.temp

# Documentation
README*.md
AGENT_MIGRATION_GUIDE.md
CONFIG_MANAGEMENT.md

# MCP config (may contain sensitive data)
.roo/