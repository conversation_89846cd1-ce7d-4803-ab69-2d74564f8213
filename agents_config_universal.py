#!/usr/bin/env python3
"""
Agno 核心配置文件
只包含启动服务所必需的基础配置
"""

# ===== 基础API配置 =====
BASE_CONFIG = {
    "api_key": "sk-dfdfymcpskfpyjpfcbkrwbgisoywffcgqgovbzubzjczprci",
    "base_url": "https://api.siliconflow.cn/v1"
}

# ===== 模型映射 =====
AVAILABLE_MODELS = {
    "kimi-72b": "moonshotai/Kimi-Dev-72B",
    "qwen3-30b": "Qwen/Qwen3-30B-A3B", 
    "qwenlong-32b": "Tongyi-Zhiwen/QwenLong-L1-32B",
    "deepseek-v3": "deepseek-ai/DeepSeek-V3"
}

# ===== 分类定义 (UI显示用) =====
CATEGORIES = {
    "legal": {"name": "法律服务", "icon": "fas fa-balance-scale", "color": "#007bff"},
    "office": {"name": "办公助手", "icon": "fas fa-briefcase", "color": "#28a745"},
    "creative": {"name": "创意写作", "icon": "fas fa-pen-fancy", "color": "#fd7e14"},
    "analysis": {"name": "数据分析", "icon": "fas fa-chart-bar", "color": "#6f42c1"},
    "workflow": {"name": "工作流程", "icon": "fas fa-stream", "color": "#20c997"},
    "team": {"name": "团队协作", "icon": "fas fa-users", "color": "#dc3545"}
}

# ===== 工具配置 (如果需要全局可用) =====
TOOLS_CONFIG = {
    "schedule_tools": {
        "class": "schedule_tools",
        "module": "tools.schedule_tools",
        "description": "日程管理工具集，支持CRUD操作",
        "functions": [
            "create_schedule",
            "get_schedules", 
            "update_schedule",
            "delete_schedule",
            "get_schedules_by_time_range"
        ]
    },
    "legal_search": {
        "class": "BaiduSearchTools",
        "module": "agno.tools.baidusearch",
        "description": "法律信息搜索工具",
        "config": {
            "fixed_max_results": 3,
            "search_domain": "legal"
        }
    }
}

# ===== 导出统一配置 =====
# 所有组件都已改为动态加载，这里只保留基础部分
UNIVERSAL_CONFIG = {
    "base": BASE_CONFIG,
    "models": AVAILABLE_MODELS,
    "tools": TOOLS_CONFIG
}

# ===== 版本信息 =====
CONFIG_VERSION = "3.0.0"
CONFIG_DESCRIPTION = "Agno核心配置 - 所有组件均已实现动态加载"

if __name__ == "__main__":
    print(f"Agno核心配置 v{CONFIG_VERSION}")
    print(f"描述: {CONFIG_DESCRIPTION}")
    print("配置加载成功！")