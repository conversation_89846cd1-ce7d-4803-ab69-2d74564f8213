
# agents/__init__.py

from .composition_agent import *
from .daily_report_agent import *
# from .legal_agent import *
from .month_report_agent import *
from .new_media_copywriting_agent import *
from .official_document_writing_agent import *
from .schedule_agent import *
from .week_report_agent import *
from .xiaohongshu_copywriting_agent import *
from .xiaohongshu_creation_agent import *

AGENTS_REGISTRY = {
    "composition_agent": composition_agent.create_agent,
    "daily_report_agent": daily_report_agent.create_agent,
    # "legal_agent": LegalAgent,
    "month_report_agent": month_report_agent.create_agent,
    "new_media_copywriting_agent": new_media_copywriting_agent.create_agent,
    "official_document_writing_agent": official_document_writing_agent.create_agent,
    "schedule_agent": schedule_agent.create_agent,
    "week_report_agent": week_report_agent.create_agent,
    "xiaohongshu_copywriting_agent": xiaohongshu_copywriting_agent.create_agent,
    "xiaohongshu_creation_agent": xiaohongshu_creation_agent.create_agent,
}

AGENTS_METADATA_FROM_FILES = {
    "composition_agent": composition_agent.AGENT_METADATA,
    "daily_report_agent": daily_report_agent.AGENT_METADATA,
    # "legal_agent": legal_agent.AGENT_METADATA,
    "month_report_agent": month_report_agent.AGENT_METADATA,
    "new_media_copywriting_agent": new_media_copywriting_agent.AGENT_METADATA,
    "official_document_writing_agent": official_document_writing_agent.AGENT_METADATA,
    "schedule_agent": schedule_agent.AGENT_METADATA,
    "week_report_agent": week_report_agent.AGENT_METADATA,
    "xiaohongshu_copywriting_agent": xiaohongshu_copywriting_agent.AGENT_METADATA,
    "xiaohongshu_creation_agent": xiaohongshu_creation_agent.AGENT_METADATA,
}

def create_agent_by_id(agent_id: str):
    if agent_id in AGENTS_REGISTRY:
        return AGENTS_REGISTRY[agent_id]()
    else:
        raise ValueError(f"找不到Agent: {agent_id}")

def get_agent_metadata_from_files(agent_id: str):
    return AGENTS_METADATA_FROM_FILES.get(agent_id, {})
