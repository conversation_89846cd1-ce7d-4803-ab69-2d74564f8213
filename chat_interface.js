// 全局变量
// 使用相对路径，让前端自动适配当前域名和端口
const API_BASE = '';
let currentMode = 'agent'; // 'agent', 'team', 'flow'
let availableAgents = [];
let availableTeams = [];
let availableFlows = [];
let availableCategories = {};
let currentCategory = null; // 当前选择的分类
let currentSearchTerm = ''; // 当前搜索词
let currentSessionKey = null;

// 会话管理 - 为每个Agent/团队/工作流维护独立的聊天历史
let chatSessions = {
    agent: {},      // { agentId: [messages] }
    team: {},       // { teamId: [messages] }
    flow: {}        // { flowId: [messages] }
};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('页面加载完成，开始初始化...');
    console.log('API_BASE:', API_BASE);
    
    try {
        await checkHealth();
        await loadCategories();
        await loadAgents();
        await loadTeams();
        await loadFlows();
        
        // 绑定事件
        setupEventListeners();
        
        // 显示欢迎消息
        addSystemMessage('👋 欢迎使用Agno统一智能平台！请从左侧选择一个Agent、Team或Flow开始对话。');
    } catch (error) {
        console.error('初始化过程中发生错误:', error);
        addSystemMessage('❌ 初始化失败，请检查控制台获取详细错误信息。');
    }
});

// 检查服务健康状态
async function checkHealth() {
    try {
        console.log('尝试连接服务器:', API_BASE);
        const response = await fetch(`${API_BASE}/api/health`, {
            mode: 'cors',
            headers: {
                'Accept': 'application/json'
            }
        });
        const health = await response.json();
        console.log('服务状态:', health);
    } catch (error) {
        console.error('服务连接失败:', error);
        addSystemMessage('❌ 服务连接失败，请检查后端服务是否正常运行。错误详情: ' + error.message);
    }
}

// 加载分类信息
async function loadCategories() {
    try {
        const response = await fetch(`${API_BASE}/api/categories`, {
            mode: 'cors',
            headers: {
                'Accept': 'application/json'
            }
        });
        const data = await response.json();
        availableCategories = data.categories;
        
        // 创建分类选择器
        createCategorySelector();
    } catch (error) {
        console.error('加载分类信息失败:', error);
    }
}

// 创建分类选择器
function createCategorySelector() {
    const sidebar = document.querySelector('.sidebar');
    const sidebarHeader = sidebar.querySelector('.sidebar-header');
    
    // 创建分类和搜索控制面板
    const controlPanel = document.createElement('div');
    controlPanel.className = 'control-panel';
    controlPanel.innerHTML = `
        <div class="category-selector">
            <select id="categorySelect">
                <option value="">所有分类</option>
            </select>
        </div>
        <div class="search-container">
            <input type="text" id="searchInput" placeholder="搜索智能体..." />
            <button id="searchButton"><i class="fas fa-search"></i></button>
            <button id="clearButton"><i class="fas fa-times"></i></button>
        </div>
    `;
    
    // 插入到sidebar-header之后
    sidebar.insertBefore(controlPanel, sidebarHeader.nextSibling);
    
    // 填充分类选项
    const categorySelect = document.getElementById('categorySelect');
    Object.entries(availableCategories).forEach(([categoryId, categoryInfo]) => {
        const option = document.createElement('option');
        option.value = categoryId;
        option.textContent = `${categoryInfo.name} (${categoryInfo.total_count})`;
        categorySelect.appendChild(option);
    });
    
    // 绑定事件
    categorySelect.addEventListener('change', handleCategoryChange);
    document.getElementById('searchInput').addEventListener('input', handleSearchInput);
    document.getElementById('searchButton').addEventListener('click', handleSearch);
    document.getElementById('clearButton').addEventListener('click', handleClearSearch);
    
    // 回车搜索
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });
}

// 处理分类变化
async function handleCategoryChange() {
    const categorySelect = document.getElementById('categorySelect');
    currentCategory = categorySelect.value || null;
    await refreshAllLists();
}

// 处理搜索输入
function handleSearchInput() {
    const searchInput = document.getElementById('searchInput');
    currentSearchTerm = searchInput.value.trim();
}

// 处理搜索
async function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    currentSearchTerm = searchInput.value.trim();
    await refreshAllLists();
}

// 清除搜索
async function handleClearSearch() {
    const searchInput = document.getElementById('searchInput');
    searchInput.value = '';
    currentSearchTerm = '';
    await refreshAllLists();
}

// 刷新所有列表
async function refreshAllLists() {
    await loadAgents(currentCategory, currentSearchTerm);
    await loadTeams(currentCategory, currentSearchTerm);
    await loadFlows(currentCategory, currentSearchTerm);
}

// 加载可用的Agent列表
async function loadAgents(category = null, search = null) {
    try {
        let url = `${API_BASE}/api/agents`;
        const params = new URLSearchParams();
        if (category) params.append('category', category);
        if (search) params.append('search', search);
        if (params.toString()) url += '?' + params.toString();
        
        const response = await fetch(url, {
            mode: 'cors',
            headers: {
                'Accept': 'application/json'
            }
        });
        const data = await response.json();
        availableAgents = data.agents;
        
        // 更新左侧列表
        const agentSection = document.querySelector('.list-section:nth-child(1)');
        const listHeader = agentSection.querySelector('.list-header');
        
        // 清空现有列表项，但保留标题
        while (agentSection.children.length > 1) {
            agentSection.removeChild(agentSection.lastChild);
        }
        
        // 添加新的Agent列表项
        data.agents.forEach(agent => {
            const listItem = createListItem(
                agent.id,
                agent.display_name || agent.name,
                agent.brief || agent.description || agent.model,
                'agent',
                getAgentIcon(agent.name),
                agent.category
            );
            agentSection.appendChild(listItem);
        });
        
        // 绑定点击事件
        bindListItemEvents();
    } catch (error) {
        console.error('加载Agent列表失败:', error);
    }
}

// 加载可用的团队列表
async function loadTeams(category = null, search = null) {
    try {
        let url = `${API_BASE}/api/teams`;
        const params = new URLSearchParams();
        if (category) params.append('category', category);
        if (search) params.append('search', search);
        if (params.toString()) url += '?' + params.toString();
        
        const response = await fetch(url, {
            mode: 'cors',
            headers: {
                'Accept': 'application/json'
            }
        });
        const data = await response.json();
        availableTeams = data.teams;
        
        // 更新左侧列表
        const teamSection = document.querySelector('.list-section:nth-child(2)');
        const listHeader = teamSection.querySelector('.list-header');
        
        // 清空现有列表项，但保留标题
        while (teamSection.children.length > 1) {
            teamSection.removeChild(teamSection.lastChild);
        }
        
        // 添加新的Team列表项
        data.teams.forEach(team => {
            const listItem = createListItem(
                team.id,
                team.display_name || team.name,
                team.brief || `${team.members.length}名成员`,
                'team',
                'fas fa-users',
                team.category
            );
            teamSection.appendChild(listItem);
        });
        
        // 绑定点击事件
        bindListItemEvents();
    } catch (error) {
        console.error('加载团队列表失败:', error);
    }
}

// 加载可用的工作流列表
async function loadFlows(category = null, search = null) {
    try {
        let url = `${API_BASE}/api/workflows`;
        const params = new URLSearchParams();
        if (category) params.append('category', category);
        if (search) params.append('search', search);
        if (params.toString()) url += '?' + params.toString();
        
        const response = await fetch(url, {
            mode: 'cors',
            headers: {
                'Accept': 'application/json'
            }
        });
        const data = await response.json();
        availableFlows = data.workflows || [];
        
        // 更新左侧列表
        const flowSection = document.querySelector('.list-section:nth-child(3)');
        const listHeader = flowSection.querySelector('.list-header');
        
        // 清空现有列表项，但保留标题
        while (flowSection.children.length > 1) {
            flowSection.removeChild(flowSection.lastChild);
        }
        
        // 添加新的Flow列表项
        availableFlows.forEach(flow => {
            const listItem = createListItem(
                flow.id,
                flow.display_name || flow.name,
                flow.brief || flow.description,
                'flow',
                getFlowIcon(flow.id),
                flow.category
            );
            flowSection.appendChild(listItem);
        });
        
        // 绑定点击事件
        bindListItemEvents();
    } catch (error) {
        console.error('加载工作流列表失败:', error);
    }
}

// 创建列表项
function createListItem(id, name, description, type, iconClass, category = null) {
    const listItem = document.createElement('div');
    listItem.className = 'list-item';
    listItem.dataset.id = id;
    listItem.dataset.type = type;
    if (category) listItem.dataset.category = category;
    
    // 获取头像URL
    const avatarUrl = getAvatarUrl(type, id);
    
    // 获取分类信息
    let categoryBadge = '';
    if (category && availableCategories[category]) {
        const categoryInfo = availableCategories[category];
        const categoryColor = getCategoryColor(category);
        categoryBadge = `<span class="category-badge" style="background-color: ${categoryColor};">${categoryInfo.name}</span>`;
    }
    
    listItem.innerHTML = `
        <div class="list-item-icon">
            <img src="${avatarUrl}" alt="${name}" style="width: 100%; height: 100%; object-fit: cover;">
        </div>
        <div class="list-item-content">
            <div class="list-item-name">${name}</div>
            <div class="list-item-desc">${description}</div>
            ${categoryBadge}
        </div>
        <div class="list-item-time"></div>
    `;
    
    return listItem;
}

// 绑定列表项点击事件
function bindListItemEvents() {
    document.querySelectorAll('.list-item').forEach(item => {
        // 先移除可能存在的旧事件监听器
        const newItem = item.cloneNode(true);
        item.parentNode.replaceChild(newItem, item);

        // 绑定新的事件监听器
        newItem.addEventListener('click', function() {
            // 移除所有active类
            document.querySelectorAll('.list-item').forEach(i => {
                i.classList.remove('active');
            });
            // 添加active类到当前项
            this.classList.add('active');

            // 更新聊天标题
            const name = this.querySelector('.list-item-name').textContent;
            document.querySelector('.chat-title').textContent = name;

            // 设置当前会话
            currentMode = this.dataset.type;
            currentSessionKey = this.dataset.id;

            // 加载会话历史
            loadSessionHistory(currentSessionKey);

            // 在移动端自动关闭侧边栏
            if (window.innerWidth <= 768) {
                document.querySelector('.sidebar').classList.remove('show');
            }

            // 显示欢迎消息
            if (!chatSessions[currentMode][currentSessionKey] ||
                chatSessions[currentMode][currentSessionKey].length === 0) {
                let welcomeMsg = '';

                if (currentMode === 'agent') {
                    const agent = availableAgents.find(a => a.id === currentSessionKey);
                    if (agent) {
                        welcomeMsg = `👋 您好！我是${agent.name}，有什么可以帮助您的吗？`;
                    }
                } else if (currentMode === 'team') {
                    const team = availableTeams.find(t => t.id === currentSessionKey);
                    if (team) {
                        welcomeMsg = `👋 您好！我们是${team.name}团队，由${team.members.length}名成员组成，有什么可以帮助您的吗？`;
                    }
                } else if (currentMode === 'flow') {
                    const flowName = getFlowDisplayName(currentSessionKey);
                    welcomeMsg = `👋 您好！这是${flowName}工作流，请输入您的需求开始流程。`;
                }

                if (welcomeMsg) {
                    addMessage(welcomeMsg, false);
                }
            }
        });
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 发送按钮点击事件
    document.getElementById('sendButton').addEventListener('click', sendMessage);
    
    // 输入框回车事件
    document.getElementById('messageInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    // 移动端菜单切换
    document.querySelector('.mobile-menu-btn').addEventListener('click', function() {
        document.querySelector('.sidebar').classList.toggle('show');
    });
}

// 发送消息 - 使用流式响应
async function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    
    if (!message) return;
    
    // 检查是否选择了会话
    if (!currentSessionKey) {
        addSystemMessage('请先从左侧选择一个Agent、Team或Flow');
        return;
    }
    
    // 添加用户消息
    addMessage(message, true);
    input.value = '';
    
    // 设置发送按钮为禁用状态
    document.getElementById('sendButton').disabled = true;
    
    try {
        if (currentMode === 'agent') {
            await sendAgentMessage(message);
        } else if (currentMode === 'team') {
            await sendTeamMessage(message);
        } else if (currentMode === 'flow') {
            await sendFlowMessage(message);
        }
    } catch (error) {
        console.error('发送消息失败:', error);
        addSystemMessage(`❌ 发送消息失败: ${error.message}`);
    } finally {
        // 恢复发送按钮状态
        document.getElementById('sendButton').disabled = false;
    }
}

// 发送Agent消息 - 使用流式响应
async function sendAgentMessage(message) {
    const response = await fetch(`${API_BASE}/api/chat`, {
        method: 'POST',
        mode: 'cors',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            agent_id: currentSessionKey,
            stream: true
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    await handleStreamResponse(response, 'agent');
}

// 发送Team消息 - 使用流式响应
async function sendTeamMessage(message) {
    const response = await fetch(`${API_BASE}/api/team`, {
        method: 'POST',
        mode: 'cors',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            team_id: currentSessionKey,
            stream: true
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    await handleStreamResponse(response, 'team');
}

// 发送Flow消息 - 使用流式响应
async function sendFlowMessage(message) {
    const response = await fetch(`${API_BASE}/api/workflow`, {
        method: 'POST',
        mode: 'cors',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            input_data: { user_message: message },
            workflow_id: currentSessionKey,
            stream: true
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    await handleStreamResponse(response, 'workflow');
}

// 添加消息
function addMessage(content, isUser = false, saveToHistory = true) {
    const messagesDiv = document.getElementById('chat-messages');

    // 移除任何流式消息
    const streamingMsg = messagesDiv.querySelector('.streaming-message');
    if (streamingMsg) {
        streamingMsg.remove();
    }

    // 创建消息容器
    const messageContainer = document.createElement('div');
    messageContainer.className = isUser ? 'user-message-container' : 'ai-message-container';

    // 创建头像
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    const img = document.createElement('img');

    // 使用正确的头像
    if (isUser) {
        img.src = getAvatarUrl('user', 'user');
        img.alt = '用户头像';
    } else {
        img.src = getAvatarUrl(currentMode, currentSessionKey);
        img.alt = `${currentSessionKey} 头像`;
    }

    avatar.appendChild(img);

    // 创建消息气泡
    const message = document.createElement('div');
    message.className = isUser ? 'message user-message' : 'message ai-message';

    // 处理Markdown内容
    try {
        const htmlContent = marked.parse(content);
        message.innerHTML = highlightCode(htmlContent);
    } catch (error) {
        message.textContent = content;
    }

    // 组装消息
    messageContainer.appendChild(avatar);
    messageContainer.appendChild(message);
    messagesDiv.appendChild(messageContainer);

    // 只有在需要时才保存到会话历史（避免加载历史时重复保存）
    if (currentSessionKey && saveToHistory) {
        saveMessageToSession(content, isUser ? 'user' : 'ai');
    }

    // 滚动到底部
    scrollToBottom();

    // 更新列表项的最后消息时间
    if (currentSessionKey) {
        updateListItemTime(currentMode, currentSessionKey);
    }

    return messageContainer;
}

// 添加系统消息
function addSystemMessage(content) {
    const messagesDiv = document.getElementById('chat-messages');
    
    // 创建系统消息
    const systemMessage = document.createElement('div');
    systemMessage.className = 'system-message';
    systemMessage.style.textAlign = 'center';
    systemMessage.style.margin = '10px 0';
    systemMessage.style.color = '#666';
    systemMessage.style.fontSize = '14px';
    
    // 处理Markdown内容
    try {
        const htmlContent = marked.parse(content);
        systemMessage.innerHTML = highlightCode(htmlContent);
    } catch (error) {
        systemMessage.textContent = content;
    }
    
    messagesDiv.appendChild(systemMessage);
    scrollToBottom();
}

// 保存消息到会话历史
function saveMessageToSession(content, type) {
    if (!currentSessionKey) return;
    
    if (!chatSessions[currentMode][currentSessionKey]) {
        chatSessions[currentMode][currentSessionKey] = [];
    }
    
    chatSessions[currentMode][currentSessionKey].push({
        content: content,
        type: type,
        timestamp: Date.now()
    });
}

// 加载会话历史
function loadSessionHistory(sessionKey) {
    if (!sessionKey) return;

    const messagesDiv = document.getElementById('chat-messages');
    messagesDiv.innerHTML = '';

    const sessionMessages = chatSessions[currentMode][sessionKey] || [];

    sessionMessages.forEach(msg => {
        // 加载历史消息时不重复保存到会话历史
        addMessage(msg.content, msg.type === 'user', false);
    });
}

// 更新列表项的最后消息时间
function updateListItemTime(type, id) {
    const listItem = document.querySelector(`.list-item[data-type="${type}"][data-id="${id}"]`);
    if (listItem) {
        const timeElement = listItem.querySelector('.list-item-time');
        timeElement.textContent = formatTime(new Date());
    }
}

// 格式化时间
function formatTime(date) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date >= today) {
        return date.getHours().toString().padStart(2, '0') + ':' + 
               date.getMinutes().toString().padStart(2, '0');
    } else if (date >= yesterday) {
        return '昨天';
    } else {
        return (date.getMonth() + 1) + '/' + date.getDate();
    }
}

// 获取Agent图标
function getAgentIcon(name) {
    const iconMap = {
        '法律顾问': 'fas fa-balance-scale',
        '代码助手': 'fas fa-code',
        '文档起草': 'fas fa-file-alt',
        '日程管理': 'fas fa-calendar-alt',
        '数据分析': 'fas fa-chart-bar'
    };
    
    // 根据名称关键词匹配图标
    for (const [key, icon] of Object.entries(iconMap)) {
        if (name.includes(key)) {
            return icon;
        }
    }
    
    return 'fas fa-robot'; // 默认图标
}

// 获取Flow图标
function getFlowIcon(flowId) {
    const iconMap = {
        'legal_document_workflow': 'fas fa-file-contract',
        'schedule_management_workflow': 'fas fa-calendar-check',
        'six_hats_workflow': 'fas fa-hat-wizard'
    };
    
    return iconMap[flowId] || 'fas fa-stream'; // 默认图标
}

// 获取Flow显示名称
function getFlowDisplayName(flowId) {
    const nameMap = {
        'legal_document_workflow': '法律文书流程',
        'schedule_management_workflow': '日程管理流程',
        'six_hats_workflow': '六顶思考帽工作流'
    };
    
    return nameMap[flowId] || flowId;
}

// 自定义代码高亮处理函数
function highlightCode(html) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    tempDiv.querySelectorAll('pre code').forEach((block) => {
        if (typeof hljs !== 'undefined') {
            hljs.highlightElement(block);
        }
    });
    
    return tempDiv.innerHTML;
}

// 处理流式响应 - 从agent_chat_example.html借鉴的实现
async function handleStreamResponse(response, type) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let currentMessage = '';
    
    try {
        while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop();
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const dataStr = line.substring(6);
                    
                    try {
                        const data = JSON.parse(dataStr);
                        
                        if (data.type === 'content') {
                            currentMessage += data.content;
                            // 实时更新消息显示
                            updateStreamingMessage(currentMessage, type);
                        } else if (data.type === 'end') {
                            if (currentMessage) {
                                addMessage(currentMessage, false);
                                currentMessage = ''; // 清空消息，避免重复添加
                            }
                        } else if (data.type === 'team_start') {
                            addSystemMessage(`🚀 团队【${data.team_name}】开始协作，协作模式: ${data.collaboration_mode}`);
                        } else if (data.type === 'team_step_start') {
                            addSystemMessage(`📝 步骤${data.step_number}: ${data.agent_role}【${data.agent_name}】开始处理...`);
                        } else if (data.type === 'team_step_content') {
                            currentMessage += data.content;
                            updateStreamingMessage(currentMessage, type);
                        } else if (data.type === 'team_step_end') {
                            if (currentMessage) {
                                addMessage(`### 【${data.agent_name}】\n\n${currentMessage}`, false);
                                currentMessage = '';
                            }
                        } else if (data.type === 'team_complete') {
                            addSystemMessage(`✅ 团队协作完成，总共 ${data.total_steps} 个步骤`);
                        } else if (data.type === 'error') {
                            addSystemMessage(`❌ 错误: ${data.error}`);
                        } else if (data.type === 'status') {
                            addSystemMessage(`ℹ️ ${data.status}`);
                        }
                    } catch (e) {
                        console.log('非JSON数据:', dataStr);
                    }
                }
            }
        }

        // 注意：不要在这里再次添加消息，因为在 'end' 事件中已经处理了
        // 这里的检查主要是为了处理异常情况
        if (currentMessage) {
            console.warn('流式响应结束时仍有未处理的消息:', currentMessage);
            addMessage(currentMessage, false);
        }

    } catch (streamError) {
        console.error('流处理错误:', streamError);
        addSystemMessage(`❌ 流处理错误: ${streamError.message}`);
    }
}

// 更新流式消息显示
function updateStreamingMessage(content, type) {
    // 查找是否有正在显示的消息，如果没有就创建一个新的
    const messagesDiv = document.getElementById('chat-messages');
    let streamingMsg = messagesDiv.querySelector('.streaming-message');
    
    if (!streamingMsg) {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'ai-message-container';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        const img = document.createElement('img');
        img.src = getAvatarUrl(currentMode, currentSessionKey);
        img.alt = `${currentSessionKey} 头像`;
        avatar.appendChild(img);
        
        streamingMsg = document.createElement('div');
        streamingMsg.className = 'message ai-message streaming-message';
        
        messageContainer.appendChild(avatar);
        messageContainer.appendChild(streamingMsg);
        messagesDiv.appendChild(messageContainer);
    }
    
    // 更新内容
    try {
        const htmlContent = marked.parse(content);
        streamingMsg.innerHTML = highlightCode(htmlContent);
    } catch (error) {
        streamingMsg.textContent = content;
    }
    
    scrollToBottom();
}

// 自动滚动到底部
function scrollToBottom() {
    const messagesDiv = document.getElementById('chat-messages');
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
}