<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Demo 配置管理器</title>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --bg-color: #f8fafc;
            --text-color: #1e293b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Helvetica Neue", Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }

        .section h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: var(--primary-color);
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .form-textarea {
            width: 100%;
            min-height: 300px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            resize: vertical;
        }

        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .agent-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .agent-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .agent-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .agent-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            border: 2px solid #e2e8f0;
        }

        .agent-info h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .agent-info p {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .agent-details {
            margin-bottom: 15px;
        }

        .agent-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .agent-detail strong {
            color: var(--text-color);
        }

        .agent-actions {
            display: flex;
            gap: 10px;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .category-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-name {
            font-weight: 600;
            color: var(--text-color);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: #fee2e2;
            color: #991b1b;
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: #fef3c7;
            color: #92400e;
            border-left: 4px solid var(--warning-color);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
        }

        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 12px;
            margin-top: 15px;
            max-height: 320px !important;
            height: 320px;
            overflow-y: scroll !important;
            overflow-x: hidden;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
            box-sizing: border-box;
        }

        /* 自定义滚动条样式 */
        .avatar-grid::-webkit-scrollbar {
            width: 10px;
        }

        .avatar-grid::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 5px;
        }

        .avatar-grid::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 5px;
        }

        .avatar-grid::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }

        /* Firefox 滚动条 */
        .avatar-grid {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) #f1f5f9;
        }

        .avatar-option {
            position: relative;
            cursor: pointer;
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
            border: 3px solid transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            aspect-ratio: 1;
            min-height: 80px;
        }

        .avatar-option:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            border-color: #cbd5e1;
        }

        .avatar-option img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 7px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .avatar-option.selected {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.3) !important;
            transform: scale(1.08) !important;
        }

        .avatar-option.selected::after {
            content: '✓';
            position: absolute;
            top: 5px;
            right: 5px;
            background: var(--primary-color);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
        }

        .code-section {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .code-title {
            color: #60a5fa;
            font-weight: 600;
        }

        .copy-btn {
            background: #374151;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #4b5563;
        }

        pre {
            background: #0f172a;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 13px;
            line-height: 1.5;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            color: #6b7280;
            margin-top: 5px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Final Demo 配置管理器</h1>
            <p>为 final-custom-layout-demo.html 管理智能体配置</p>
        </div>

        <div id="alerts"></div>

        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="agent-count">0</div>
                <div class="stat-label">智能体数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="category-count">0</div>
                <div class="stat-label">分类数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="config-size">0</div>
                <div class="stat-label">配置大小 (KB)</div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="section">
            <h2>🎯 快速操作</h2>
            <button class="btn btn-primary" onclick="loadCurrentConfig()">
                📥 加载当前配置
            </button>
            <button class="btn btn-success" onclick="openAddAgentModal()">
                ➕ 添加智能体
            </button>
            <button class="btn btn-success" onclick="openAddCategoryModal()">
                📁 添加分类
            </button>
            <button class="btn btn-secondary" onclick="exportConfig()">
                💾 导出配置
            </button>
            <button class="btn btn-secondary" onclick="importConfig()">
                📂 导入配置
            </button>
            <button class="btn btn-danger" onclick="resetToDefault()">
                🔄 重置默认
            </button>
            <input type="file" id="fileInput" accept=".json" style="display: none;" onchange="handleFileImport(event)">
        </div>

        <!-- 分类管理 -->
        <div class="section">
            <h2>📁 分类管理</h2>
            <div id="categories-container" class="category-grid">
                <!-- 分类将在这里显示 -->
            </div>
        </div>

        <!-- 智能体管理 -->
        <div class="section">
            <h2>🤖 智能体管理</h2>
            <div id="agents-container" class="agent-grid">
                <!-- 智能体将在这里显示 -->
            </div>
        </div>

        <!-- 代码生成 -->
        <div class="section">
            <h2>💻 生成代码</h2>
            <p>点击下面的按钮生成可以直接替换到 final-custom-layout-demo.html 中的配置代码：</p>
            <button class="btn btn-primary" onclick="generateCode()">
                🔧 生成配置代码
            </button>
            <div id="generated-code" class="code-section" style="display: none;">
                <div class="code-header">
                    <span class="code-title">生成的配置代码</span>
                    <button class="copy-btn" onclick="copyCode()">复制代码</button>
                </div>
                <pre id="code-content"></pre>
            </div>
        </div>

        <!-- JSON 编辑器 -->
        <div class="section">
            <h2>📝 JSON 编辑器</h2>
            <p>高级用户可以直接编辑 JSON 配置：</p>
            <textarea id="json-editor" class="form-textarea" placeholder="JSON 配置将显示在这里..."></textarea>
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="validateJSON()">
                    ✅ 验证 JSON
                </button>
                <button class="btn btn-success" onclick="applyJSON()">
                    🔄 应用配置
                </button>
            </div>
        </div>
    </div>

    <!-- 添加智能体模态框 -->
    <div id="agent-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="agent-modal-title">添加智能体</h3>
                <button class="close-btn" onclick="closeAgentModal()">&times;</button>
            </div>
            <form id="agent-form">
                <div class="form-group">
                    <label class="form-label">智能体名称</label>
                    <input type="text" id="agent-name" class="form-input" placeholder="例如：抖音分析专家" required>
                </div>
                <div class="form-group">
                    <label class="form-label">描述</label>
                    <input type="text" id="agent-description" class="form-input" placeholder="例如：专业的抖音数据分析" required>
                </div>
                <div class="form-group">
                    <label class="form-label">分类</label>
                    <select id="agent-category" class="form-select" required>
                        <!-- 分类选项将动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Bot ID</label>
                    <input type="text" id="agent-bot-id" class="form-input" placeholder="例如：7523880023146168366"
                        required>
                </div>
                <div class="form-group">
                    <label class="form-label">选择头像</label>
                    <div id="avatar-selector" class="avatar-grid">
                        <!-- 头像选项将动态加载 -->
                    </div>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeAgentModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加分类模态框 -->
    <div id="category-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="category-modal-title">添加分类</h3>
                <button class="close-btn" onclick="closeCategoryModal()">&times;</button>
            </div>
            <form id="category-form">
                <div class="form-group">
                    <label class="form-label">分类名称</label>
                    <input type="text" id="category-name" class="form-input" placeholder="例如：媒体部" required>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeCategoryModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局配置数据
        let configData = {
            agents: [],
            categories: []
        };

        // 当前编辑的项目
        let currentEditingAgent = null;
        let currentEditingCategory = null;

        // 可用的头像列表 - 包含touxiang文件夹中的所有头像
        const availableAvatars = [
            'touxiang/11.png', 'touxiang/12.png', 'touxiang/13.png', 'touxiang/16.png',
            'touxiang/19.png', 'touxiang/21.png', 'touxiang/213.png', 'touxiang/3.png',
            'touxiang/4.png', 'touxiang/5.png', 'touxiang/7.png', 'touxiang/8.png',
            'touxiang/881.png', 'touxiang/882.png', 'touxiang/883.png', 'touxiang/884.png',
            'touxiang/885.png', 'touxiang/886.png', 'touxiang/887.png', 'touxiang/888.png',
            'touxiang/889.png', 'touxiang/890.png', 'touxiang/9.png', 'touxiang/991.png',
            'touxiang/992.png', 'touxiang/993.png', 'touxiang/994.png', 'touxiang/995.png',
            'touxiang/996.png', 'touxiang/997.png', 'touxiang/998.png', 'touxiang/999.png',
            'touxiang/abca.png', 'touxiang/abgag.png', 'touxiang/afaf.png', 'touxiang/aga.png',
            'touxiang/agag.png', 'touxiang/agasf.png', 'touxiang/agasga.png', 'touxiang/agha.png',
            'touxiang/agva.png'
        ];

        // 生成唯一ID
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
        }

        // 显示提示消息
        function showAlert(message, type = 'success') {
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alertsContainer.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('agent-count').textContent = configData.agents.length;
            document.getElementById('category-count').textContent = configData.categories.length;

            const configSize = (JSON.stringify(configData).length / 1024).toFixed(1);
            document.getElementById('config-size').textContent = configSize;
        }

        // 加载当前配置
        function loadCurrentConfig() {
            // 从 final-custom-layout-demo.html 中提取的默认配置
            const defaultConfig = {
                "agents": [
                    { "id": "mdgy8weu9oeb0", "name": "抖音账号分析专家", "description": "抖音账号分析", "category": "mdgzqt479yixm", "bot_id": "7523880023146168366", "avatar": "touxiang/abgag.png" },
                    { "id": "mdgy8weufsow9", "name": "公文写作专家", "description": "专业写作公文", "category": "mdgzqt479vyew", "bot_id": "7528255758204076072", "avatar": "touxiang/8.png" },
                    { "id": "mdgy8weu3xbkt", "name": "日报专家", "description": "帮助写一份日报", "category": "mdgzqt479vyew", "bot_id": "7528256246898622518", "avatar": "touxiang/12.png" },
                    { "id": "mdgy8weubknlv", "name": "小红书图文总结专家", "description": "总结图文笔记内容", "category": "mdgzqt479yixm", "bot_id": "7524620947187056679", "avatar": "touxiang/aga.png" }
                ],
                "categories": [
                    { "id": "mdgzqt479yixm", "name": "媒体部" },
                    { "id": "mdgzqt47ynyhu", "name": "法律部" },
                    { "id": "mdgzqt479vyew", "name": "行政部" }
                ]
            };

            configData = defaultConfig;
            renderAll();
            showAlert('当前配置已加载！');
        }

        // 渲染所有内容
        function renderAll() {
            renderCategories();
            renderAgents();
            updateCategoryOptions();
            updateJSONEditor();
            updateStats();
        }

        // 渲染分类
        function renderCategories() {
            const container = document.getElementById('categories-container');
            container.innerHTML = '';

            configData.categories.forEach(category => {
                const categoryCard = document.createElement('div');
                categoryCard.className = 'category-card';
                categoryCard.innerHTML = `
                    <span class="category-name">${category.name}</span>
                    <div>
                        <button class="btn btn-secondary" style="padding: 6px 12px; margin-right: 5px;" onclick="editCategory('${category.id}')">编辑</button>
                        <button class="btn btn-danger" style="padding: 6px 12px;" onclick="deleteCategory('${category.id}')">删除</button>
                    </div>
                `;
                container.appendChild(categoryCard);
            });
        }

        // 渲染智能体
        function renderAgents() {
            const container = document.getElementById('agents-container');
            container.innerHTML = '';

            configData.agents.forEach(agent => {
                const category = configData.categories.find(c => c.id === agent.category);
                const categoryName = category ? category.name : '未分类';

                const agentCard = document.createElement('div');
                agentCard.className = 'agent-card';
                agentCard.innerHTML = `
                    <div class="agent-header">
                        <img src="${agent.avatar}" alt="${agent.name}" class="agent-avatar">
                        <div class="agent-info">
                            <h3>${agent.name}</h3>
                            <p>${agent.description}</p>
                        </div>
                    </div>
                    <div class="agent-details">
                        <div class="agent-detail">
                            <span>分类:</span>
                            <strong>${categoryName}</strong>
                        </div>
                        <div class="agent-detail">
                            <span>Bot ID:</span>
                            <strong>${agent.bot_id}</strong>
                        </div>
                        <div class="agent-detail">
                            <span>ID:</span>
                            <strong>${agent.id}</strong>
                        </div>
                    </div>
                    <div class="agent-actions">
                        <button class="btn btn-secondary" onclick="editAgent('${agent.id}')">编辑</button>
                        <button class="btn btn-danger" onclick="deleteAgent('${agent.id}')">删除</button>
                    </div>
                `;
                container.appendChild(agentCard);
            });
        }

        // 更新分类选项
        function updateCategoryOptions() {
            const select = document.getElementById('agent-category');
            select.innerHTML = '';

            configData.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            });
        }

        // 更新JSON编辑器
        function updateJSONEditor() {
            document.getElementById('json-editor').value = JSON.stringify(configData, null, 2);
        }

        // 打开添加智能体模态框
        function openAddAgentModal() {
            currentEditingAgent = null;
            document.getElementById('agent-modal-title').textContent = '添加智能体';
            document.getElementById('agent-form').reset();
            loadAvatarOptions();
            updateCategoryOptions();
            document.getElementById('agent-modal').classList.add('active');
        }

        // 编辑智能体
        function editAgent(agentId) {
            const agent = configData.agents.find(a => a.id === agentId);
            if (!agent) return;

            currentEditingAgent = agent;
            document.getElementById('agent-modal-title').textContent = '编辑智能体';
            document.getElementById('agent-name').value = agent.name;
            document.getElementById('agent-description').value = agent.description;
            document.getElementById('agent-category').value = agent.category;
            document.getElementById('agent-bot-id').value = agent.bot_id;

            loadAvatarOptions();
            updateCategoryOptions();

            // 选中当前头像
            setTimeout(() => {
                const avatarOption = document.querySelector(`[data-avatar="${agent.avatar}"]`);
                if (avatarOption) {
                    avatarOption.classList.add('selected');
                }
            }, 100);

            document.getElementById('agent-modal').classList.add('active');
        }

        // 删除智能体
        function deleteAgent(agentId) {
            if (confirm('确定要删除这个智能体吗？')) {
                configData.agents = configData.agents.filter(a => a.id !== agentId);
                renderAll();
                showAlert('智能体已删除！', 'success');
            }
        }

        // 关闭智能体模态框
        function closeAgentModal() {
            document.getElementById('agent-modal').classList.remove('active');
        }

        // 加载头像选项
        function loadAvatarOptions() {
            const container = document.getElementById('avatar-selector');
            container.innerHTML = '';

            // 添加滚动提示
            const hint = document.createElement('div');
            hint.style.gridColumn = '1 / -1';
            hint.style.textAlign = 'center';
            hint.style.padding = '10px';
            hint.style.color = '#6b7280';
            hint.style.fontSize = '13px';
            hint.style.background = '#e2e8f0';
            hint.style.borderRadius = '6px';
            hint.style.marginBottom = '10px';
            hint.innerHTML = '📸 滚动查看更多头像 (共 ' + availableAvatars.length + ' 个)';
            container.appendChild(hint);

            // 创建头像选项
            availableAvatars.forEach((avatar, index) => {
                const option = document.createElement('div');
                option.className = 'avatar-option';
                option.setAttribute('data-avatar', avatar);
                option.title = `头像 ${index + 1}`;

                const img = document.createElement('img');
                img.src = avatar;
                img.alt = `头像 ${index + 1}`;
                // 移除内联样式，使用CSS类样式

                img.onerror = function () {
                    option.style.display = 'none';
                    console.warn('头像加载失败:', avatar);
                };

                option.appendChild(img);
                option.onclick = () => selectAvatar(option, avatar);
                container.appendChild(option);
            });

            // 强制触发滚动条显示并测试滚动
            setTimeout(() => {
                container.scrollTop = 1;
                container.scrollTop = 0;

                // 默认选择第一个头像（如果是新增模式）
                if (!currentEditingAgent) {
                    const firstOption = container.querySelector('.avatar-option:not([style*="display: none"])');
                    if (firstOption) {
                        selectAvatar(firstOption, firstOption.getAttribute('data-avatar'));
                    }
                }

                console.log('头像容器信息:', {
                    clientHeight: container.clientHeight,
                    scrollHeight: container.scrollHeight,
                    canScroll: container.scrollHeight > container.clientHeight
                });
            }, 100);
        }

        // 选择头像
        function selectAvatar(element, avatar) {
            document.querySelectorAll('.avatar-option').forEach(opt => opt.classList.remove('selected'));
            element.classList.add('selected');
        }

        // 智能体表单提交
        document.getElementById('agent-form').addEventListener('submit', function (e) {
            e.preventDefault();

            const name = document.getElementById('agent-name').value;
            const description = document.getElementById('agent-description').value;
            const category = document.getElementById('agent-category').value;
            const botId = document.getElementById('agent-bot-id').value;
            const selectedAvatar = document.querySelector('.avatar-option.selected');

            if (!selectedAvatar) {
                showAlert('请选择一个头像！', 'danger');
                return;
            }

            const avatar = selectedAvatar.getAttribute('data-avatar');

            if (currentEditingAgent) {
                // 编辑现有智能体
                currentEditingAgent.name = name;
                currentEditingAgent.description = description;
                currentEditingAgent.category = category;
                currentEditingAgent.bot_id = botId;
                currentEditingAgent.avatar = avatar;
                showAlert('智能体已更新！');
            } else {
                // 添加新智能体
                const newAgent = {
                    id: generateId(),
                    name,
                    description,
                    category,
                    bot_id: botId,
                    avatar
                };
                configData.agents.push(newAgent);
                showAlert('智能体已添加！');
            }

            renderAll();
            closeAgentModal();
        });

        // 打开添加分类模态框
        function openAddCategoryModal() {
            currentEditingCategory = null;
            document.getElementById('category-modal-title').textContent = '添加分类';
            document.getElementById('category-form').reset();
            document.getElementById('category-modal').classList.add('active');
        }

        // 编辑分类
        function editCategory(categoryId) {
            const category = configData.categories.find(c => c.id === categoryId);
            if (!category) return;

            currentEditingCategory = category;
            document.getElementById('category-modal-title').textContent = '编辑分类';
            document.getElementById('category-name').value = category.name;
            document.getElementById('category-modal').classList.add('active');
        }

        // 删除分类
        function deleteCategory(categoryId) {
            const agentsInCategory = configData.agents.filter(a => a.category === categoryId);
            if (agentsInCategory.length > 0) {
                showAlert('无法删除分类：该分类下还有智能体！', 'danger');
                return;
            }

            if (confirm('确定要删除这个分类吗？')) {
                configData.categories = configData.categories.filter(c => c.id !== categoryId);
                renderAll();
                showAlert('分类已删除！', 'success');
            }
        }

        // 关闭分类模态框
        function closeCategoryModal() {
            document.getElementById('category-modal').classList.remove('active');
        }

        // 分类表单提交
        document.getElementById('category-form').addEventListener('submit', function (e) {
            e.preventDefault();

            const name = document.getElementById('category-name').value;

            if (currentEditingCategory) {
                // 编辑现有分类
                currentEditingCategory.name = name;
                showAlert('分类已更新！');
            } else {
                // 添加新分类
                const newCategory = {
                    id: generateId(),
                    name
                };
                configData.categories.push(newCategory);
                showAlert('分类已添加！');
            }

            renderAll();
            closeCategoryModal();
        });

        // 生成配置代码
        function generateCode() {
            const code = `const hardcodedData = ${JSON.stringify(configData, null, 4)};`;
            document.getElementById('code-content').textContent = code;
            document.getElementById('generated-code').style.display = 'block';
            showAlert('配置代码已生成！');
        }

        // 复制代码
        function copyCode() {
            const code = document.getElementById('code-content').textContent;
            navigator.clipboard.writeText(code).then(() => {
                showAlert('代码已复制到剪贴板！');
            });
        }

        // 导出配置
        function exportConfig() {
            const blob = new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'final-demo-config.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            showAlert('配置已导出！');
        }

        // 导入配置
        function importConfig() {
            document.getElementById('fileInput').click();
        }

        // 处理文件导入
        function handleFileImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function (e) {
                try {
                    const importedConfig = JSON.parse(e.target.result);
                    configData = importedConfig;
                    renderAll();
                    showAlert('配置导入成功！');
                } catch (error) {
                    showAlert('导入失败：JSON 格式错误！', 'danger');
                }
            };
            reader.readAsText(file);
        }

        // 重置为默认配置
        function resetToDefault() {
            if (confirm('确定要重置为默认配置吗？这将清除所有当前配置！')) {
                loadCurrentConfig();
            }
        }

        // 验证JSON
        function validateJSON() {
            try {
                const jsonText = document.getElementById('json-editor').value;
                const parsed = JSON.parse(jsonText);

                // 基本验证
                if (!parsed.agents || !Array.isArray(parsed.agents)) {
                    throw new Error('缺少 agents 数组');
                }
                if (!parsed.categories || !Array.isArray(parsed.categories)) {
                    throw new Error('缺少 categories 数组');
                }

                showAlert('JSON 格式验证通过！');
            } catch (error) {
                showAlert('JSON 验证失败：' + error.message, 'danger');
            }
        }

        // 应用JSON配置
        function applyJSON() {
            try {
                const jsonText = document.getElementById('json-editor').value;
                const parsed = JSON.parse(jsonText);
                configData = parsed;
                renderAll();
                showAlert('JSON 配置已应用！');
            } catch (error) {
                showAlert('应用失败：' + error.message, 'danger');
            }
        }

        // 页面加载时初始化
        window.onload = function () {
            loadCurrentConfig();
        };
    </script>
</body>

</html>